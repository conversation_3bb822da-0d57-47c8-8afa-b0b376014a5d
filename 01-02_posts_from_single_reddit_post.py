import re
from bs4 import BeautifulSoup
from datetime import datetime
import pytz

def convert_to_local_time(utc_time_str, target_timezone='US/Eastern'):
    """
    Converts a UTC timestamp string to a formatted local time string.

    Args:
        utc_time_str: UTC timestamp string (e.g., '2024-07-05T15:52:00.000Z')
        target_timezone: Target timezone (default: 'US/Eastern')
                        Other examples: 'US/Pacific', 'US/Central', 'US/Mountain', 'UTC'

    Returns:
        Formatted time string in YYYYMMDDHHMM format
    """
    try:
        # Parse UTC time
        utc_time = datetime.fromisoformat(utc_time_str.replace('Z', '+00:00'))

        # Convert to target timezone
        utc_tz = pytz.UTC
        target_tz = pytz.timezone(target_timezone)

        # Localize to UTC first, then convert to target timezone
        utc_time = utc_tz.localize(utc_time.replace(tzinfo=None))
        local_time = utc_time.astimezone(target_tz)

        return local_time.strftime('%Y%m%d%H%M')
    except Exception as e:
        print(f"Error converting timestamp '{utc_time_str}': {e}")
        return ""

def extract_reddit_post_info(html_content):
    soup = BeautifulSoup(html_content, 'lxml')
    
    # --- Extract Post Information ---
    post_element = soup.find('shreddit-post')
    if not post_element:
        return "No post found."

    post_title = post_element.get('post-title', 'No Title')
    post_url_slug = post_element.get('permalink', '')
    post_url_match = re.match(r'(/r/[^/]+/comments/[^/]+/)', post_url_slug)
    post_url = f"https://www.reddit.com{post_url_match.group(1)}" if post_url_match else ""
    
    created_timestamp_str = post_element.get('created-timestamp', '')
    formatted_post_time = convert_to_edt(created_timestamp_str) if created_timestamp_str else ""

    output = [f"- {formatted_post_time} - [{post_title}]({post_url})"]
        
    return "\n".join(output)

import pyperclip

if __name__ == "__main__":
    try:
        html_content = pyperclip.paste()
        
        if not html_content:
            print("Clipboard is empty.")
        else:
            result = extract_reddit_post_info(html_content)
            pyperclip.copy(result)
            print(result)
            print("\nResults copied to clipboard.")
            
    except Exception as e:
        print(f"An error occurred: {e}")
